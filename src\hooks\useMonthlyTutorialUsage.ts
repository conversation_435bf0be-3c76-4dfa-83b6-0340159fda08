
import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/integrations/supabase/client';

interface MonthlyUsage {
  monthlyTutorialsCreated: number;
  maxTutorialsPerMonth: number;
  currentBillingCycleStart: string | null;
  currentBillingCycleEnd: string | null;
  loading: boolean;
  error: string | null;
}

export const useMonthlyTutorialUsage = (): MonthlyUsage => {
  const { session } = useAuth();
  const [usage, setUsage] = useState<MonthlyUsage>({
    monthlyTutorialsCreated: 0,
    maxTutorialsPerMonth: 0,
    currentBillingCycleStart: null,
    currentBillingCycleEnd: null,
    loading: true,
    error: null,
  });

  useEffect(() => {
    if (!session?.user?.id) {
      setUsage(prev => ({ ...prev, loading: false }));
      return;
    }

    const fetchUsage = async () => {
      try {
        setUsage(prev => ({ ...prev, loading: true, error: null }));

        // Get user details with tier information and billing cycle data
        const { data: userDetails, error: userError } = await supabase
          .from('user_details')
          .select(`
            monthly_tutorials_created,
            current_billing_cycle_start,
            current_billing_cycle_end,
            tier,
            stripe_subscription_id
          `)
          .eq('id', session.user.id)
          .single();

        if (userError) throw userError;

        // Get tier limits
        const { data: tierSettings, error: tierError } = await supabase
          .from('tier_settings')
          .select('max_tutorials_per_month')
          .eq('tier_name', userDetails.tier)
          .single();

        if (tierError) throw tierError;

        // Check if billing cycle needs to be initialized or updated
        const now = new Date();
        const billingCycleEnd = userDetails.current_billing_cycle_end ? new Date(userDetails.current_billing_cycle_end) : null;
        
        // If user has a Stripe subscription, their billing cycle should be managed by Stripe
        // If no Stripe subscription, use calendar month cycles
        let effectiveBillingStart = userDetails.current_billing_cycle_start;
        let effectiveBillingEnd = userDetails.current_billing_cycle_end;
        
        if (!userDetails.stripe_subscription_id && (!billingCycleEnd || billingCycleEnd <= now)) {
          // For free tier users, initialize or reset to calendar month
          const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
          const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
          
          effectiveBillingStart = startOfMonth.toISOString();
          effectiveBillingEnd = endOfMonth.toISOString();
          
          // Update the database for free tier users
          await supabase
            .from('user_details')
            .update({
              current_billing_cycle_start: effectiveBillingStart,
              current_billing_cycle_end: effectiveBillingEnd,
              monthly_tutorials_created: 0
            })
            .eq('id', session.user.id);
        }

        setUsage({
          monthlyTutorialsCreated: userDetails.monthly_tutorials_created || 0,
          maxTutorialsPerMonth: tierSettings.max_tutorials_per_month || 0,
          currentBillingCycleStart: effectiveBillingStart,
          currentBillingCycleEnd: effectiveBillingEnd,
          loading: false,
          error: null,
        });
      } catch (err) {
        console.error('Error fetching monthly usage:', err);
        setUsage(prev => ({
          ...prev,
          loading: false,
          error: err instanceof Error ? err.message : 'Failed to fetch usage data',
        }));
      }
    };

    fetchUsage();
  }, [session?.user?.id]);

  return usage;
};
