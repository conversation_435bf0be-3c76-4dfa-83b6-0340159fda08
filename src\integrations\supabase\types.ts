export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      openrouter_usage: {
        Row: {
          completion_tokens: number | null
          cost: number | null
          created_at: string
          id: string
          model: string
          prompt_text: string | null
          prompt_tokens: number | null
          response_text: string | null
          session_id: string | null
          temperature: number | null
          total_tokens: number | null
          tutorial_id: string | null
          use_cache: boolean | null
          user_id: string | null
        }
        Insert: {
          completion_tokens?: number | null
          cost?: number | null
          created_at?: string
          id?: string
          model: string
          prompt_text?: string | null
          prompt_tokens?: number | null
          response_text?: string | null
          session_id?: string | null
          temperature?: number | null
          total_tokens?: number | null
          tutorial_id?: string | null
          use_cache?: boolean | null
          user_id?: string | null
        }
        Update: {
          completion_tokens?: number | null
          cost?: number | null
          created_at?: string
          id?: string
          model?: string
          prompt_text?: string | null
          prompt_tokens?: number | null
          response_text?: string | null
          session_id?: string | null
          temperature?: number | null
          total_tokens?: number | null
          tutorial_id?: string | null
          use_cache?: boolean | null
          user_id?: string | null
        }
        Relationships: []
      }
      subscribers: {
        Row: {
          created_at: string
          email: string
          id: string
          stripe_customer_id: string | null
          subscribed: boolean
          subscription_end: string | null
          subscription_tier: string | null
          updated_at: string
          user_id: string | null
        }
        Insert: {
          created_at?: string
          email: string
          id?: string
          stripe_customer_id?: string | null
          subscribed?: boolean
          subscription_end?: string | null
          subscription_tier?: string | null
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          created_at?: string
          email?: string
          id?: string
          stripe_customer_id?: string | null
          subscribed?: boolean
          subscription_end?: string | null
          subscription_tier?: string | null
          updated_at?: string
          user_id?: string | null
        }
        Relationships: []
      }
      tier_settings: {
        Row: {
          can_access_private_repos: boolean
          created_at: string
          features: string[]
          has_api_access: boolean
          has_custom_branding: boolean
          has_priority_support: boolean
          has_trial: boolean
          id: string
          max_file_size_mb: number
          max_tutorials_per_month: number
          price_monthly: number
          tier_name: string
          trial_days: number
          trial_tutorials_limit: number
          updated_at: string
        }
        Insert: {
          can_access_private_repos?: boolean
          created_at?: string
          features?: string[]
          has_api_access?: boolean
          has_custom_branding?: boolean
          has_priority_support?: boolean
          has_trial?: boolean
          id?: string
          max_file_size_mb?: number
          max_tutorials_per_month?: number
          price_monthly?: number
          tier_name: string
          trial_days?: number
          trial_tutorials_limit?: number
          updated_at?: string
        }
        Update: {
          can_access_private_repos?: boolean
          created_at?: string
          features?: string[]
          has_api_access?: boolean
          has_custom_branding?: boolean
          has_priority_support?: boolean
          has_trial?: boolean
          id?: string
          max_file_size_mb?: number
          max_tutorials_per_month?: number
          price_monthly?: number
          tier_name?: string
          trial_days?: number
          trial_tutorials_limit?: number
          updated_at?: string
        }
        Relationships: []
      }
      tutorial_jobs: {
        Row: {
          completed_at: string | null
          created_at: string
          current_stage: string | null
          error_message: string | null
          id: string
          input_parameters: Json
          progress: number | null
          result: Json | null
          started_at: string | null
          status: Database["public"]["Enums"]["job_status"]
          updated_at: string
          user_id: string
        }
        Insert: {
          completed_at?: string | null
          created_at?: string
          current_stage?: string | null
          error_message?: string | null
          id?: string
          input_parameters: Json
          progress?: number | null
          result?: Json | null
          started_at?: string | null
          status?: Database["public"]["Enums"]["job_status"]
          updated_at?: string
          user_id: string
        }
        Update: {
          completed_at?: string | null
          created_at?: string
          current_stage?: string | null
          error_message?: string | null
          id?: string
          input_parameters?: Json
          progress?: number | null
          result?: Json | null
          started_at?: string | null
          status?: Database["public"]["Enums"]["job_status"]
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      tutorial_metadata: {
        Row: {
          background_color: string | null
          chapter_urls: Json
          cover_url: string | null
          created_at: string
          description: string | null
          difficulty: string | null
          featured: boolean | null
          id: string
          index_url: string
          is_public: boolean
          language: string | null
          programming_language:
            | Database["public"]["Enums"]["programming_language"]
            | null
          project_name: string
          repo_branch: string | null
          repo_commit_sha: string | null
          repo_name: string | null
          repo_owner: string | null
          repo_path: string | null
          repo_url: string | null
          tags: string[] | null
          tutorial_id: string
          updated_at: string | null
          user_id: string | null
          views: number | null
        }
        Insert: {
          background_color?: string | null
          chapter_urls: Json
          cover_url?: string | null
          created_at?: string
          description?: string | null
          difficulty?: string | null
          featured?: boolean | null
          id?: string
          index_url: string
          is_public?: boolean
          language?: string | null
          programming_language?:
            | Database["public"]["Enums"]["programming_language"]
            | null
          project_name: string
          repo_branch?: string | null
          repo_commit_sha?: string | null
          repo_name?: string | null
          repo_owner?: string | null
          repo_path?: string | null
          repo_url?: string | null
          tags?: string[] | null
          tutorial_id: string
          updated_at?: string | null
          user_id?: string | null
          views?: number | null
        }
        Update: {
          background_color?: string | null
          chapter_urls?: Json
          cover_url?: string | null
          created_at?: string
          description?: string | null
          difficulty?: string | null
          featured?: boolean | null
          id?: string
          index_url?: string
          is_public?: boolean
          language?: string | null
          programming_language?:
            | Database["public"]["Enums"]["programming_language"]
            | null
          project_name?: string
          repo_branch?: string | null
          repo_commit_sha?: string | null
          repo_name?: string | null
          repo_owner?: string | null
          repo_path?: string | null
          repo_url?: string | null
          tags?: string[] | null
          tutorial_id?: string
          updated_at?: string | null
          user_id?: string | null
          views?: number | null
        }
        Relationships: []
      }
      user_details: {
        Row: {
          active: boolean
          created_at: string | null
          current_billing_cycle_end: string | null
          current_billing_cycle_start: string | null
          default_llm: string | null
          email: string | null
          first_name: string | null
          github: string | null
          id: string
          image_url: string | null
          is_admin: boolean
          last_name: string | null
          max_file_size: number
          monthly_tutorials_created: number
          stripe_subscription_id: string | null
          tier: Database["public"]["Enums"]["user_tier"]
          trial_end_date: string | null
          trial_start_date: string | null
          trial_tutorials_limit: number | null
          tutorials_created_count: number | null
          updated_at: string | null
          user_name: string
        }
        Insert: {
          active?: boolean
          created_at?: string | null
          current_billing_cycle_end?: string | null
          current_billing_cycle_start?: string | null
          default_llm?: string | null
          email?: string | null
          first_name?: string | null
          github?: string | null
          id?: string
          image_url?: string | null
          is_admin?: boolean
          last_name?: string | null
          max_file_size?: number
          monthly_tutorials_created?: number
          stripe_subscription_id?: string | null
          tier?: Database["public"]["Enums"]["user_tier"]
          trial_end_date?: string | null
          trial_start_date?: string | null
          trial_tutorials_limit?: number | null
          tutorials_created_count?: number | null
          updated_at?: string | null
          user_name?: string
        }
        Update: {
          active?: boolean
          created_at?: string | null
          current_billing_cycle_end?: string | null
          current_billing_cycle_start?: string | null
          default_llm?: string | null
          email?: string | null
          first_name?: string | null
          github?: string | null
          id?: string
          image_url?: string | null
          is_admin?: boolean
          last_name?: string | null
          max_file_size?: number
          monthly_tutorials_created?: number
          stripe_subscription_id?: string | null
          tier?: Database["public"]["Enums"]["user_tier"]
          trial_end_date?: string | null
          trial_start_date?: string | null
          trial_tutorials_limit?: number | null
          tutorials_created_count?: number | null
          updated_at?: string | null
          user_name?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      can_user_create_tutorial: {
        Args: { user_id: string }
        Returns: boolean
      }
      can_user_create_tutorial_monthly: {
        Args: { user_id: string }
        Returns: boolean
      }
      is_user_in_trial: {
        Args: { user_id: string }
        Returns: boolean
      }
      reset_monthly_tutorial_counts: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      update_user_billing_cycle: {
        Args: {
          p_user_id: string
          p_stripe_subscription_id: string
          p_current_period_start: string
          p_current_period_end: string
        }
        Returns: undefined
      }
    }
    Enums: {
      job_status: "queued" | "processing" | "completed" | "failed" | "cancelled"
      programming_language:
        | "javascript"
        | "typescript"
        | "python"
        | "java"
        | "csharp"
        | "cpp"
        | "c"
        | "go"
        | "rust"
        | "php"
        | "ruby"
        | "swift"
        | "kotlin"
        | "dart"
        | "scala"
        | "haskell"
        | "clojure"
        | "elixir"
        | "erlang"
        | "lua"
        | "perl"
        | "r"
        | "matlab"
        | "shell"
        | "sql"
        | "html"
        | "css"
        | "other"
      user_tier: "Spark" | "Propel" | "Apex"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      job_status: ["queued", "processing", "completed", "failed", "cancelled"],
      programming_language: [
        "javascript",
        "typescript",
        "python",
        "java",
        "csharp",
        "cpp",
        "c",
        "go",
        "rust",
        "php",
        "ruby",
        "swift",
        "kotlin",
        "dart",
        "scala",
        "haskell",
        "clojure",
        "elixir",
        "erlang",
        "lua",
        "perl",
        "r",
        "matlab",
        "shell",
        "sql",
        "html",
        "css",
        "other",
      ],
      user_tier: ["Spark", "Propel", "Apex"],
    },
  },
} as const
